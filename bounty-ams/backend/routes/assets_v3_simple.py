"""
资产管理 V3.0 简化API路由
直接操作Elasticsearch，避免复杂的依赖关系
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Dict, Any, Optional
import json
from datetime import datetime

from auth import get_current_user
from models_dynamic import User
from elasticsearch_client import get_es_client

router = APIRouter(prefix="/assets-v3-simple", tags=["assets-v3-simple"])


@router.get("/search")
async def search_assets_simple(
    q: Optional[str] = Query(None, description="搜索关键词"),
    asset_type: Optional[str] = Query(None, description="资产类型"),
    data_source: Optional[str] = Query(None, description="数据源"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    es_client = Depends(get_es_client),
    current_user: User = Depends(get_current_user)
):
    """简化的资产搜索接口"""
    try:
        # 构建查询
        query = {"match_all": {}}
        
        if q or asset_type or data_source:
            bool_query = {"bool": {"must": []}}
            
            if q:
                bool_query["bool"]["must"].append({
                    "multi_match": {
                        "query": q,
                        "fields": ["data.value", "data.name", "full_text"]
                    }
                })
            
            if asset_type:
                bool_query["bool"]["must"].append({
                    "term": {"metadata.asset_type": asset_type}
                })
            
            if data_source:
                bool_query["bool"]["must"].append({
                    "term": {"metadata.data_source": data_source}
                })
            
            query = bool_query
        
        # 计算分页
        from_offset = (page - 1) * size
        
        # 执行搜索
        response = await es_client.search(
            index="assets-current",
            body={
                "query": query,
                "from": from_offset,
                "size": size,
                "sort": [{"metadata.created_at": {"order": "desc"}}]
            }
        )
        
        # 处理结果
        assets = []
        for hit in response["hits"]["hits"]:
            assets.append({
                "id": hit["_id"],
                "score": hit["_score"],
                "source": hit["_source"]
            })
        
        total = response["hits"]["total"]["value"]
        
        return {
            "success": True,
            "data": {
                "assets": assets,
                "total": total,
                "page": page,
                "size": size,
                "pages": (total + size - 1) // size,
                "took": response["took"]
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索失败: {str(e)}"
        )


@router.get("/statistics")
async def get_statistics_simple(
    es_client = Depends(get_es_client),
    current_user: User = Depends(get_current_user)
):
    """简化的统计信息接口"""
    try:
        # 基础统计
        response = await es_client.search(
            index="assets-current",
            body={
                "size": 0,
                "aggs": {
                    "asset_types": {
                        "terms": {"field": "metadata.asset_type", "size": 20}
                    },
                    "data_sources": {
                        "terms": {"field": "metadata.data_source", "size": 10}
                    },
                    "quality_levels": {
                        "terms": {"field": "metadata.quality_level", "size": 5}
                    }
                }
            }
        )
        
        total = response["hits"]["total"]["value"]
        aggs = response["aggregations"]
        
        return {
            "success": True,
            "data": {
                "total_assets": total,
                "asset_types": [
                    {"key": bucket["key"], "count": bucket["doc_count"]}
                    for bucket in aggs["asset_types"]["buckets"]
                ],
                "data_sources": [
                    {"key": bucket["key"], "count": bucket["doc_count"]}
                    for bucket in aggs["data_sources"]["buckets"]
                ],
                "quality_levels": [
                    {"key": bucket["key"], "count": bucket["doc_count"]}
                    for bucket in aggs["quality_levels"]["buckets"]
                ]
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {str(e)}"
        )


@router.post("/ingest")
async def ingest_assets_simple(
    assets_data: List[Dict[str, Any]],
    es_client = Depends(get_es_client),
    current_user: User = Depends(get_current_user)
):
    """简化的资产摄取接口"""
    try:
        success_count = 0
        errors = []
        
        for i, asset_data in enumerate(assets_data):
            try:
                # 构建标准资产文档
                doc = {
                    "metadata": {
                        "asset_id": f"simple_{datetime.utcnow().timestamp()}_{i}",
                        "asset_type": asset_data.get("asset_type", "other"),
                        "data_source": asset_data.get("data_source", "manual_import"),
                        "confidence": asset_data.get("confidence", 1.0),
                        "quality_level": "medium",
                        "processing_status": "processed",
                        "created_at": datetime.utcnow().isoformat(),
                        "updated_at": datetime.utcnow().isoformat()
                    },
                    "data": {
                        "value": asset_data.get("value", ""),
                        "name": asset_data.get("name"),
                        "description": asset_data.get("description"),
                        "tags": asset_data.get("tags", []),
                        "attributes": asset_data.get("attributes", {})
                    },
                    "relation": {
                        "platform_id": asset_data.get("platform_id"),
                        "project_id": asset_data.get("project_id"),
                        "parent_asset_id": None,
                        "related_assets": []
                    },
                    "processing": {
                        "dedup_hash": f"simple_{hash(asset_data.get('value', ''))}",
                        "validation_errors": [],
                        "processing_notes": "简化摄取"
                    },
                    "full_text": f"{asset_data.get('value', '')} {asset_data.get('name', '')} {asset_data.get('description', '')}",
                    "@timestamp": datetime.utcnow().isoformat()
                }
                
                # 插入到ES
                await es_client.index(
                    index="assets-current",
                    id=doc["metadata"]["asset_id"],
                    body=doc
                )
                
                success_count += 1
                
            except Exception as e:
                errors.append(f"资产 {i}: {str(e)}")
        
        # 刷新索引
        await es_client.indices.refresh(index="assets-current")
        
        return {
            "success": True,
            "data": {
                "total_processed": len(assets_data),
                "success_count": success_count,
                "error_count": len(errors),
                "errors": errors
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"摄取失败: {str(e)}"
        )


@router.get("/{asset_id}")
async def get_asset_simple(
    asset_id: str,
    es_client = Depends(get_es_client),
    current_user: User = Depends(get_current_user)
):
    """获取单个资产"""
    try:
        response = await es_client.get(
            index="assets-current",
            id=asset_id
        )
        
        return {
            "success": True,
            "data": {
                "id": response["_id"],
                "source": response["_source"]
            }
        }
        
    except Exception as e:
        if "not_found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="资产不存在"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取资产失败: {str(e)}"
            )
