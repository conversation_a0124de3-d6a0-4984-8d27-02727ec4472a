#!/usr/bin/env python3
"""
资产管理V2统一系统初始化脚本
用于创建索引模板、迁移现有数据到统一格式
"""

import asyncio
import json
from datetime import datetime
from elasticsearch import AsyncElasticsearch
from sqlalchemy.ext.asyncio import AsyncSession
from database import AsyncSessionLocal
from elasticsearch_client import get_es_client
from asset_management_v2_unified import create_unified_asset_manager, DataSource, AssetStatus, ConfidenceLevel, AssetMetadata

async def check_elasticsearch_connection():
    """检查Elasticsearch连接"""
    try:
        es = await get_es_client()
        health = await es.cluster.health()
        print(f"✅ Elasticsearch连接正常: {health['status']}")
        return es
    except Exception as e:
        print(f"❌ Elasticsearch连接失败: {e}")
        return None

async def migrate_existing_data(es_client: AsyncElasticsearch, asset_manager):
    """迁移现有数据到统一格式"""
    print("\n🔄 开始迁移现有数据...")
    
    # 迁移 assets-2025-07 索引的数据
    try:
        print("📥 迁移 assets-2025-07 索引数据...")
        
        # 查询现有数据
        query = {
            "query": {"match_all": {}},
            "size": 1000
        }
        
        response = await es_client.search(index="assets-2025-07", body=query)
        hits = response.get("hits", {}).get("hits", [])
        
        if not hits:
            print("⚠️  assets-2025-07 索引中没有数据")
        else:
            print(f"📊 找到 {len(hits)} 条数据")
            
            # 转换数据格式
            raw_assets = []
            for hit in hits:
                source = hit["_source"]
                
                # 转换为统一格式
                asset_data = {
                    "asset_type": source.get("asset_type", "unknown"),
                    "asset_value": source.get("asset_value", ""),
                    "asset_host": source.get("asset_host"),
                    "asset_port": source.get("port"),
                    "asset_service": source.get("service"),
                    "tags": source.get("tags", []),
                    "platform_id": source.get("platform_id"),
                    "project_id": source.get("project_id"),
                    "discovered_at": source.get("discovered_at") or source.get("timestamp"),
                    "confidence": source.get("confidence", "medium"),
                    "status": source.get("status", "active"),
                    "source": source.get("source", "legacy_migration"),
                    "metadata": {
                        "migrated_from": "assets-2025-07",
                        "original_id": hit["_id"],
                        "migration_time": datetime.utcnow().isoformat()
                    }
                }
                
                raw_assets.append(asset_data)
            
            # 批量处理
            if raw_assets:
                metadata = AssetMetadata(
                    source=DataSource.DYNAMIC_MODEL,
                    confidence=ConfidenceLevel.MEDIUM,
                    status=AssetStatus.ACTIVE,
                    tags=["migrated", "legacy"],
                    discovered_at=datetime.utcnow()
                )
                
                result = await asset_manager.process_assets_unified(
                    raw_assets=raw_assets,
                    source=DataSource.DYNAMIC_MODEL,
                    metadata=metadata,
                    enable_dedup=True,
                    enable_cleaning=True
                )
                
                print(f"✅ assets-2025-07 迁移完成: 处理 {result.total_processed}, 成功 {result.success_count}, 重复 {result.duplicate_count}")
            
    except Exception as e:
        print(f"❌ 迁移 assets-2025-07 失败: {e}")
    
    # 迁移 enhanced_asset-2025-07 索引的数据
    try:
        print("📥 迁移 enhanced_asset-2025-07 索引数据...")
        
        response = await es_client.search(index="enhanced_asset-2025-07", body=query)
        hits = response.get("hits", {}).get("hits", [])
        
        if not hits:
            print("⚠️  enhanced_asset-2025-07 索引中没有数据")
        else:
            print(f"📊 找到 {len(hits)} 条数据")
            
            raw_assets = []
            for hit in hits:
                source = hit["_source"]
                
                asset_data = {
                    "asset_type": source.get("asset_type", "unknown"),
                    "asset_value": source.get("asset_value", ""),
                    "asset_host": source.get("asset_host"),
                    "asset_port": source.get("port"),
                    "asset_service": source.get("service"),
                    "tags": source.get("tags", []),
                    "platform_id": source.get("platform_id"),
                    "project_id": source.get("project_id"),
                    "discovered_at": source.get("discovered_at") or source.get("timestamp"),
                    "confidence": source.get("confidence", "high"),  # enhanced数据置信度更高
                    "status": source.get("status", "verified"),
                    "source": source.get("source", "enhanced_migration"),
                    "metadata": {
                        "migrated_from": "enhanced_asset-2025-07",
                        "original_id": hit["_id"],
                        "migration_time": datetime.utcnow().isoformat(),
                        "enhanced": True
                    }
                }
                
                raw_assets.append(asset_data)
            
            if raw_assets:
                metadata = AssetMetadata(
                    source=DataSource.AGENT_DISCOVERY,
                    confidence=ConfidenceLevel.HIGH,
                    status=AssetStatus.VERIFIED,
                    tags=["migrated", "enhanced"],
                    discovered_at=datetime.utcnow()
                )
                
                result = await asset_manager.process_assets_unified(
                    raw_assets=raw_assets,
                    source=DataSource.AGENT_DISCOVERY,
                    metadata=metadata,
                    enable_dedup=True,
                    enable_cleaning=True
                )
                
                print(f"✅ enhanced_asset-2025-07 迁移完成: 处理 {result.total_processed}, 成功 {result.success_count}, 重复 {result.duplicate_count}")
            
    except Exception as e:
        print(f"❌ 迁移 enhanced_asset-2025-07 失败: {e}")

async def verify_migration(es_client: AsyncElasticsearch):
    """验证迁移结果"""
    print("\n🔍 验证迁移结果...")
    
    try:
        # 检查统一索引
        response = await es_client.search(
            index="unified-assets",
            body={
                "query": {"match_all": {}},
                "size": 0,
                "aggs": {
                    "total_count": {"value_count": {"field": "asset_id"}},
                    "by_source": {"terms": {"field": "source", "size": 10}},
                    "by_type": {"terms": {"field": "asset_type", "size": 10}}
                }
            }
        )
        
        total = response["hits"]["total"]["value"]
        aggs = response["aggregations"]
        
        print(f"📊 统一索引总数据量: {total}")
        print("📈 按来源分布:")
        for bucket in aggs["by_source"]["buckets"]:
            print(f"  - {bucket['key']}: {bucket['doc_count']}")
        
        print("📈 按类型分布:")
        for bucket in aggs["by_type"]["buckets"]:
            print(f"  - {bucket['key']}: {bucket['doc_count']}")
        
        if total > 0:
            print("✅ 数据迁移验证成功！")
        else:
            print("⚠️  统一索引中没有数据，请检查迁移过程")
            
    except Exception as e:
        print(f"❌ 验证迁移结果失败: {e}")

async def main():
    """主函数"""
    print("🚀 资产管理V2统一系统初始化开始...")
    
    # 1. 检查Elasticsearch连接
    es_client = await check_elasticsearch_connection()
    if not es_client:
        return
    
    # 2. 获取数据库会话
    try:
        async with AsyncSessionLocal() as db_session:
            print("✅ 数据库连接正常")
            
            # 3. 创建资产管理器（这会自动创建索引模板和别名）
            print("\n🏗️  初始化资产管理器...")
            asset_manager = await create_unified_asset_manager(db_session)
            print("✅ 资产管理器初始化完成")
            
            # 4. 迁移现有数据
            await migrate_existing_data(es_client, asset_manager)
            
            # 5. 验证迁移结果
            await verify_migration(es_client)
            
            print("\n🎉 资产管理V2统一系统初始化完成！")
            print("💡 现在可以访问前端界面: http://localhost:5175/assets-v2-unified")
            
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
