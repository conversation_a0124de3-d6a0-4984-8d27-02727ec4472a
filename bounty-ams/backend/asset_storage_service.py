"""
资产存储和搜索服务
基于Elasticsearch的高性能资产存储和搜索
"""

from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import json
import logging
from dataclasses import asdict
from elasticsearch import AsyncElasticsearch
from elasticsearch.helpers import async_bulk

# 避免循环导入，使用类型注解
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from asset_management_v3 import Asset
    from asset_index_manager import AssetIndexManager

logger = logging.getLogger(__name__)


class AssetStorageService:
    """资产存储服务"""
    
    def __init__(self, es_client: AsyncElasticsearch, index_manager: AssetIndexManager):
        self.es_client = es_client
        self.index_manager = index_manager
        self.batch_size = 100
        
    async def store_asset(self, asset: Asset, index_name: Optional[str] = None) -> bool:
        """存储单个资产"""
        try:
            if index_name is None:
                index_name = await self.index_manager.get_current_index()
            
            # 准备文档
            doc = self._prepare_document(asset)
            
            # 存储到ES
            response = await self.es_client.index(
                index=index_name,
                id=asset.metadata.asset_id,
                body=doc
            )
            
            logger.debug(f"存储资产成功: {asset.metadata.asset_id}")
            return response["result"] in ["created", "updated"]
            
        except Exception as e:
            logger.error(f"存储资产失败: {e}")
            return False
    
    async def store_assets_batch(
        self,
        assets: List[Asset],
        index_name: Optional[str] = None
    ) -> Tuple[int, int, List[str]]:
        """批量存储资产
        
        Returns:
            Tuple[int, int, List[str]]: (成功数量, 失败数量, 错误信息列表)
        """
        if index_name is None:
            index_name = await self.index_manager.get_current_index()
        
        success_count = 0
        error_count = 0
        errors = []
        
        # 准备批量操作
        actions = []
        for asset in assets:
            try:
                doc = self._prepare_document(asset)
                action = {
                    "_index": index_name,
                    "_id": asset.metadata.asset_id,
                    "_source": doc
                }
                actions.append(action)
                
            except Exception as e:
                error_count += 1
                errors.append(f"准备文档失败 {asset.metadata.asset_id}: {e}")
        
        # 执行批量操作
        if actions:
            try:
                async for ok, action in async_bulk(
                    self.es_client,
                    actions,
                    chunk_size=self.batch_size,
                    max_retries=3,
                    initial_backoff=2,
                    max_backoff=600
                ):
                    if ok:
                        success_count += 1
                    else:
                        error_count += 1
                        errors.append(f"批量操作失败: {action}")
                        
            except Exception as e:
                logger.error(f"批量存储失败: {e}")
                errors.append(f"批量存储异常: {e}")
        
        logger.info(f"批量存储完成: 成功 {success_count}, 失败 {error_count}")
        return success_count, error_count, errors
    
    async def update_asset(
        self,
        asset_id: str,
        updates: Dict[str, Any],
        index_name: Optional[str] = None
    ) -> bool:
        """更新资产"""
        try:
            if index_name is None:
                index_name = await self.index_manager.get_current_index()
            
            response = await self.es_client.update(
                index=index_name,
                id=asset_id,
                body={"doc": updates}
            )
            
            return response["result"] == "updated"
            
        except Exception as e:
            logger.error(f"更新资产失败: {e}")
            return False
    
    async def delete_asset(
        self,
        asset_id: str,
        index_name: Optional[str] = None
    ) -> bool:
        """删除资产"""
        try:
            if index_name is None:
                index_name = await self.index_manager.get_current_index()
            
            response = await self.es_client.delete(
                index=index_name,
                id=asset_id
            )
            
            return response["result"] == "deleted"
            
        except Exception as e:
            logger.error(f"删除资产失败: {e}")
            return False
    
    async def get_asset(
        self,
        asset_id: str,
        index_name: Optional[str] = None
    ) -> Optional[Asset]:
        """获取单个资产"""
        try:
            if index_name is None:
                index_name = "assets-current"  # 使用别名
            
            response = await self.es_client.get(
                index=index_name,
                id=asset_id
            )
            
            return Asset.from_dict(response["_source"])
            
        except Exception as e:
            logger.error(f"获取资产失败: {e}")
            return None
    
    def _prepare_document(self, asset: Asset) -> Dict[str, Any]:
        """准备ES文档"""
        doc = asset.to_dict()
        
        # 添加时间戳
        doc["@timestamp"] = datetime.utcnow().isoformat()
        
        # 构建全文搜索字段
        full_text_parts = [
            asset.data.value,
            asset.data.name or "",
            asset.data.description or "",
            " ".join(asset.data.tags),
            " ".join(str(v) for v in asset.data.attributes.values() if v)
        ]
        doc["full_text"] = " ".join(filter(None, full_text_parts))
        
        return doc


class AssetSearchService:
    """资产搜索服务"""
    
    def __init__(self, es_client: AsyncElasticsearch):
        self.es_client = es_client
        self.default_size = 20
        self.max_size = 1000
        
    async def search_assets(
        self,
        query: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None,
        sort: Optional[List[Dict[str, str]]] = None,
        page: int = 1,
        size: int = 20,
        index_name: str = "assets-current"
    ) -> Dict[str, Any]:
        """搜索资产"""
        try:
            # 构建查询
            es_query = self._build_search_query(query, filters)
            
            # 构建排序
            es_sort = self._build_sort(sort)
            
            # 计算分页
            from_offset = (page - 1) * size
            size = min(size, self.max_size)
            
            # 执行搜索
            response = await self.es_client.search(
                index=index_name,
                body={
                    "query": es_query,
                    "sort": es_sort,
                    "from": from_offset,
                    "size": size,
                    "track_total_hits": True
                }
            )
            
            # 处理结果
            assets = []
            for hit in response["hits"]["hits"]:
                try:
                    asset = Asset.from_dict(hit["_source"])
                    assets.append({
                        "asset": asset,
                        "score": hit["_score"],
                        "id": hit["_id"]
                    })
                except Exception as e:
                    logger.error(f"解析搜索结果失败: {e}")
                    continue
            
            total = response["hits"]["total"]["value"]
            
            return {
                "assets": assets,
                "total": total,
                "page": page,
                "size": size,
                "pages": (total + size - 1) // size,
                "took": response["took"]
            }
            
        except Exception as e:
            logger.error(f"搜索资产失败: {e}")
            return {
                "assets": [],
                "total": 0,
                "page": page,
                "size": size,
                "pages": 0,
                "error": str(e)
            }
    
    async def aggregate_assets(
        self,
        aggregations: Dict[str, Any],
        query: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None,
        index_name: str = "assets-current"
    ) -> Dict[str, Any]:
        """聚合分析资产"""
        try:
            # 构建查询
            es_query = self._build_search_query(query, filters)
            
            # 执行聚合
            response = await self.es_client.search(
                index=index_name,
                body={
                    "query": es_query,
                    "aggs": aggregations,
                    "size": 0  # 只返回聚合结果
                }
            )
            
            return {
                "aggregations": response["aggregations"],
                "total": response["hits"]["total"]["value"],
                "took": response["took"]
            }
            
        except Exception as e:
            logger.error(f"聚合分析失败: {e}")
            return {"error": str(e)}
    
    async def get_asset_statistics(
        self,
        index_name: str = "assets-current"
    ) -> Dict[str, Any]:
        """获取资产统计信息"""
        try:
            # 基础统计聚合
            aggs = {
                "asset_types": {
                    "terms": {
                        "field": "metadata.asset_type",
                        "size": 50
                    }
                },
                "data_sources": {
                    "terms": {
                        "field": "metadata.data_source",
                        "size": 20
                    }
                },
                "quality_levels": {
                    "terms": {
                        "field": "metadata.quality_level",
                        "size": 10
                    }
                },
                "processing_status": {
                    "terms": {
                        "field": "metadata.processing_status",
                        "size": 10
                    }
                },
                "platforms": {
                    "terms": {
                        "field": "relation.platform_id",
                        "size": 20
                    }
                },
                "projects": {
                    "terms": {
                        "field": "relation.project_id",
                        "size": 50
                    }
                },
                "daily_counts": {
                    "date_histogram": {
                        "field": "metadata.created_at",
                        "calendar_interval": "day",
                        "min_doc_count": 0,
                        "extended_bounds": {
                            "min": (datetime.utcnow() - timedelta(days=30)).isoformat(),
                            "max": datetime.utcnow().isoformat()
                        }
                    }
                },
                "confidence_stats": {
                    "stats": {
                        "field": "metadata.confidence"
                    }
                }
            }
            
            result = await self.aggregate_assets(aggs, index_name=index_name)
            
            if "error" in result:
                return result
            
            # 处理统计结果
            stats = {
                "total_assets": result["total"],
                "asset_types": self._process_terms_agg(result["aggregations"]["asset_types"]),
                "data_sources": self._process_terms_agg(result["aggregations"]["data_sources"]),
                "quality_levels": self._process_terms_agg(result["aggregations"]["quality_levels"]),
                "processing_status": self._process_terms_agg(result["aggregations"]["processing_status"]),
                "platforms": self._process_terms_agg(result["aggregations"]["platforms"]),
                "projects": self._process_terms_agg(result["aggregations"]["projects"]),
                "daily_counts": self._process_date_histogram(result["aggregations"]["daily_counts"]),
                "confidence_stats": result["aggregations"]["confidence_stats"]
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {"error": str(e)}
    
    def _build_search_query(
        self,
        query: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """构建搜索查询"""
        if not query and not filters:
            return {"match_all": {}}
        
        bool_query = {"bool": {"must": [], "filter": []}}
        
        # 文本查询
        if query:
            bool_query["bool"]["must"].append({
                "multi_match": {
                    "query": query,
                    "fields": [
                        "data.value^3",
                        "data.name^2",
                        "data.description",
                        "full_text"
                    ],
                    "type": "best_fields",
                    "fuzziness": "AUTO"
                }
            })
        
        # 过滤条件
        if filters:
            for field, value in filters.items():
                if isinstance(value, list):
                    bool_query["bool"]["filter"].append({
                        "terms": {field: value}
                    })
                elif isinstance(value, dict):
                    # 范围查询
                    if "gte" in value or "lte" in value or "gt" in value or "lt" in value:
                        bool_query["bool"]["filter"].append({
                            "range": {field: value}
                        })
                else:
                    bool_query["bool"]["filter"].append({
                        "term": {field: value}
                    })
        
        return bool_query
    
    def _build_sort(self, sort: Optional[List[Dict[str, str]]] = None) -> List[Dict[str, Any]]:
        """构建排序"""
        if not sort:
            return [{"metadata.created_at": {"order": "desc"}}]
        
        es_sort = []
        for sort_item in sort:
            for field, order in sort_item.items():
                es_sort.append({field: {"order": order}})
        
        return es_sort
    
    def _process_terms_agg(self, agg_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """处理terms聚合结果"""
        return [
            {"key": bucket["key"], "count": bucket["doc_count"]}
            for bucket in agg_result["buckets"]
        ]
    
    def _process_date_histogram(self, agg_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """处理日期直方图聚合结果"""
        return [
            {
                "date": bucket["key_as_string"],
                "timestamp": bucket["key"],
                "count": bucket["doc_count"]
            }
            for bucket in agg_result["buckets"]
        ]
